import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'lib/config/app_config.dart';
import 'lib/services/environment_service.dart';

/// مثال شامل لاستخدام جميع إعدادات التشخيص
class DebugUsageExample {
  
  /// 1️⃣ DEBUG_MODE - في وحدة التحكم
  static void logDebugInfo(String message) {
    if (AppConfig.debugMode) {
      debugPrint('🔍 [DEBUG] $message');
    }
  }

  /// 2️⃣ VERBOSE_LOGGING - تسجيل مفصل
  static void logVerbose(String operation, Map<String, dynamic> details) {
    if (AppConfig.verboseLogging) {
      debugPrint('📝 [VERBOSE] $operation');
      details.forEach((key, value) {
        debugPrint('   └─ $key: $value');
      });
    }
  }

  /// مثال لاستخدام التسجيل في عملية تسجيل الدخول
  static Future<bool> loginWithDebug(String email, String password) async {
    // تسجيل بداية العملية
    logDebugInfo('بدء عملية تسجيل الدخول');
    
    if (AppConfig.verboseLogging) {
      logVerbose('تسجيل الدخول', {
        'email': email,
        'timestamp': DateTime.now().toIso8601String(),
        'server': EnvironmentService.getServerUrl(),
        'database': EnvironmentService.getDatabase(),
      });
    }

    try {
      // محاكاة عملية تسجيل الدخول
      await Future.delayed(Duration(seconds: 2));
      
      logDebugInfo('تم تسجيل الدخول بنجاح');
      return true;
      
    } catch (e) {
      logDebugInfo('فشل في تسجيل الدخول: $e');
      
      if (AppConfig.verboseLogging) {
        logVerbose('خطأ تسجيل الدخول', {
          'error': e.toString(),
          'email': email,
          'server_status': 'unknown',
          'retry_count': 1,
        });
      }
      
      return false;
    }
  }
}

/// 3️⃣ SHOW_DEBUG_INFO - عرض على الشاشة
class ScreenWithDebugInfo extends StatelessWidget {
  const ScreenWithDebugInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('شاشة مع معلومات التشخيص'),
        // إضافة مؤشر وضع التطوير في شريط التطبيق
        backgroundColor: AppConfig.showDebugInfo 
            ? Colors.red.withOpacity(0.8) 
            : null,
      ),
      body: Column(
        children: [
          // شريط تحذيري في الأعلى (يظهر فقط في وضع التطوير)
          if (AppConfig.showDebugInfo)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8.0),
              color: Colors.orange.withOpacity(0.2),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'وضع التطوير مفعل',
                    style: TextStyle(
                      color: Colors.orange[800],
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

          // المحتوى الأساسي
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('محتوى الشاشة الأساسي'),
                  const SizedBox(height: 20),
                  
                  ElevatedButton(
                    onPressed: () {
                      // مثال لاستخدام التسجيل
                      DebugUsageExample.loginWithDebug(
                        '<EMAIL>', 
                        'password123'
                      );
                    },
                    child: Text('اختبار تسجيل الدخول'),
                  ),
                ],
              ),
            ),
          ),

          // معلومات التشخيص في الأسفل
          if (AppConfig.showDebugInfo)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12.0),
              color: Colors.grey[100],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات التشخيص:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'خادم: ${EnvironmentService.getServerUrl()}',
                    style: TextStyle(fontSize: 10),
                  ),
                  Text(
                    'قاعدة البيانات: ${EnvironmentService.getDatabase()}',
                    style: TextStyle(fontSize: 10),
                  ),
                  Text(
                    'إصدار: ${EnvironmentService.getAppVersion()}',
                    style: TextStyle(fontSize: 10),
                  ),
                  Text(
                    'وضع التشخيص: ${AppConfig.debugMode ? "مفعل" : "معطل"}',
                    style: TextStyle(fontSize: 10),
                  ),
                ],
              ),
            ),
        ],
      ),
      
      // زر عائم لمعلومات التشخيص التفصيلية
      floatingActionButton: AppConfig.showDebugInfo
          ? FloatingActionButton.small(
              onPressed: () => _showDetailedDebugInfo(context),
              backgroundColor: Colors.red.withOpacity(0.8),
              child: const Icon(Icons.info_outline, color: Colors.white),
            )
          : null,
    );
  }

  void _showDetailedDebugInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.bug_report, color: Colors.red),
            const SizedBox(width: 8),
            Text('معلومات التشخيص التفصيلية'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('خادم', EnvironmentService.getServerUrl()),
              _buildDetailRow('قاعدة البيانات', EnvironmentService.getDatabase()),
              _buildDetailRow('إصدار التطبيق', EnvironmentService.getAppVersion()),
              const Divider(),
              _buildDetailRow('وضع التشخيص', AppConfig.debugMode ? "مفعل" : "معطل"),
              _buildDetailRow('التسجيل المفصل', AppConfig.verboseLogging ? "مفعل" : "معطل"),
              _buildDetailRow('عرض معلومات التشخيص', AppConfig.showDebugInfo ? "مفعل" : "معطل"),
              _buildDetailRow('طبقة مراقبة الأداء', AppConfig.enablePerformanceOverlay ? "مفعل" : "معطل"),
              const Divider(),
              _buildDetailRow('Firebase', EnvironmentService.isFirebaseEnabled() ? "مفعل" : "معطل"),
              _buildDetailRow('Certificate Pinning', AppConfig.enableCertificatePinning ? "مفعل" : "معطل"),
              _buildDetailRow('مهلة الجلسة', '${AppConfig.defaultSessionTimeoutMinutes} دقيقة'),
              const Divider(),
              _buildDetailRow('وضع البناء', kDebugMode ? "تطوير" : "إنتاج"),
              _buildDetailRow('Platform', Theme.of(context).platform.name),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }
}

/// 4️⃣ ENABLE_PERFORMANCE_OVERLAY - يظهر تلقائياً على الشاشة
/// لا يحتاج كود إضافي، يتم تفعيله في MaterialApp
/// ويظهر كطبقة شفافة تحتوي على:
/// - معدل الإطارات (FPS)
/// - استهلاك الذاكرة
/// - مؤشرات الأداء
