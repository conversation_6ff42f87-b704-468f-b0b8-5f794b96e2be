import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import '../services/environment_service.dart';
import 'device_info_service.dart';

/// خدمة Firebase Analytics لمراقبة استخدام التطبيق وسلوك المستخدمين
/// تتيح تتبع الأحداث، الشاشات، وخصائص المستخدمين بطريقة آمنة ومتوافقة مع GDPR
class FirebaseAnalyticsService {
  static FirebaseAnalytics? _analytics;
  static FirebaseAnalyticsObserver? _observer;
  static bool _isInitialized = false;

  /// الحصول على مثيل Firebase Analytics
  static FirebaseAnalytics? get analytics => _analytics;

  /// الحصول على مراقب Firebase Analytics للتنقل
  static FirebaseAnalyticsObserver? get observer => _observer;

  /// تهيئة Firebase Analytics
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // التحقق من تفعيل Firebase في الإعدادات
      if (!EnvironmentService.isFirebaseEnabled() ||
          !EnvironmentService.isFirebaseAnalyticsEnabled()) {
        if (kDebugMode) {
          debugPrint('🔥 Firebase Analytics معطل في الإعدادات');
        }
        return;
      }

      // تهيئة Firebase Core إذا لم يكن مهيأ
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }

      // تهيئة Analytics
      _analytics = FirebaseAnalytics.instance;
      _observer = FirebaseAnalyticsObserver(analytics: _analytics!);

      // تعيين خصائص التطبيق الأساسية
      await _setDefaultUserProperties();

      // تعيين وضع التشخيص
      await _analytics!.setAnalyticsCollectionEnabled(
        EnvironmentService.isAnalyticsCollectCustomEventsEnabled(),
      );

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('✅ تم تهيئة Firebase Analytics بنجاح!');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تهيئة Firebase Analytics: $e');
      }
    }
  }

  /// تعيين خصائص المستخدم الافتراضية
  static Future<void> _setDefaultUserProperties() async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      await _analytics!.setUserProperty(
        name: 'app_version',
        value: EnvironmentService.getAppVersion(),
      );

      await _analytics!.setUserProperty(
        name: 'app_environment',
        value: kDebugMode ? 'development' : 'production',
      );

      await _analytics!.setUserProperty(
        name: 'platform',
        value: defaultTargetPlatform.name,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين خصائص المستخدم: $e');
      }
    }
  }

  /// تسجيل حدث مخصص
  static Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectCustomEventsEnabled()) {
      return;
    }

    try {
      await _analytics!.logEvent(name: name, parameters: parameters);

      if (kDebugMode) {
        debugPrint('📊 تم تسجيل حدث Analytics: $name');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل حدث Analytics: $e');
      }
    }
  }

  /// تسجيل مشاهدة شاشة
  static Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectScreenViewsEnabled()) {
      return;
    }

    try {
      await _analytics!.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
        parameters: parameters,
      );

      if (kDebugMode) {
        debugPrint('📱 تم تسجيل مشاهدة شاشة: $screenName');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل مشاهدة الشاشة: $e');
      }
    }
  }

  /// تسجيل تسجيل الدخول
  static Future<void> logLogin({
    required String method,
    bool? success,
    String? errorCode,
  }) async {
    final parameters = <String, Object>{'login_method': method};

    if (success != null) {
      parameters['success'] = success ? 'true' : 'false';
    }

    if (errorCode != null) {
      parameters['error_code'] = errorCode;
    }

    await logEvent(name: 'login', parameters: parameters);
  }

  /// تسجيل تسجيل الخروج
  static Future<void> logLogout({
    required String method,
    bool? dataCleared,
  }) async {
    final parameters = <String, Object>{'logout_method': method};

    if (dataCleared != null) {
      parameters['data_cleared'] = dataCleared ? 'true' : 'false';
    }

    await logEvent(name: 'logout', parameters: parameters);
  }

  /// تسجيل طلب إجازة
  static Future<void> logLeaveRequest({
    required String leaveType,
    required int daysRequested,
    required String status,
  }) async {
    await logEvent(
      name: 'leave_request_submitted',
      parameters: {
        'leave_type': leaveType,
        'days_requested': daysRequested,
        'status': status,
      },
    );
  }

  /// تسجيل موافقة على إجازة
  static Future<void> logLeaveApproval({
    required String action,
    required String leaveType,
    required int daysRequested,
  }) async {
    await logEvent(
      name: 'leave_approval_action',
      parameters: {
        'action': action, // approve, reject, validate
        'leave_type': leaveType,
        'days_requested': daysRequested,
      },
    );
  }

  /// تسجيل تغيير كلمة المرور
  static Future<void> logPasswordChange({
    required bool success,
    String? errorCode,
  }) async {
    await logEvent(
      name: 'password_change',
      parameters: {
        'success': success ? 'true' : 'false',
        if (errorCode != null) 'error_code': errorCode,
      },
    );
  }

  /// تسجيل استخدام المصادقة البيومترية
  static Future<void> logBiometricAuth({
    required String action, // enable, disable, authenticate
    required bool success,
    String? errorCode,
  }) async {
    await logEvent(
      name: 'biometric_auth',
      parameters: {
        'action': action,
        'success': success ? 'true' : 'false',
        if (errorCode != null) 'error_code': errorCode,
      },
    );
  }

  /// تسجيل تغيير اللغة
  static Future<void> logLanguageChange({
    required String fromLanguage,
    required String toLanguage,
  }) async {
    await logEvent(
      name: 'language_change',
      parameters: {'from_language': fromLanguage, 'to_language': toLanguage},
    );
  }

  /// تسجيل خطأ في التطبيق
  static Future<void> logError({
    required String errorType,
    required String errorMessage,
    String? screenName,
    String? functionName,
  }) async {
    await logEvent(
      name: 'app_error',
      parameters: {
        'error_type': errorType,
        'error_message': errorMessage,
        if (screenName != null) 'screen_name': screenName,
        if (functionName != null) 'function_name': functionName,
      },
    );
  }

  /// تسجيل أداء العمليات
  static Future<void> logPerformance({
    required String operationType,
    required int durationMs,
    bool? success,
    String? details,
  }) async {
    await logEvent(
      name: 'performance_metric',
      parameters: {
        'operation_type': operationType,
        'duration_ms': durationMs,
        if (success != null) 'success': success ? 'true' : 'false',
        if (details != null) 'details': details,
      },
    );
  }

  /// تعيين معرف المستخدم (موحد مع Crashlytics)
  static Future<void> setUserId(String? userId) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      // استخدام نفس المعرف في Analytics و Crashlytics للتوحيد
      await _analytics!.setUserId(id: userId);

      if (kDebugMode) {
        debugPrint('👤 تم تعيين معرف المستخدم في Analytics: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين معرف المستخدم: $e');
      }
    }
  }

  /// إعداد معلومات السياق الشاملة للمستخدم والجهاز
  static Future<void> setUserContextInfo({
    required String userId,
    String? userRole,
    String? department,
    String? loginMethod,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      // تسجيل معرف المستخدم
      await setUserId(userId);

      // جمع معلومات الجهاز والنظام
      final deviceSummary = await DeviceInfoService.getDeviceSummary();

      // إعداد خصائص المستخدم الأساسية
      final userProperties = <String, String>{
        'user_role': userRole ?? 'unknown',
        'department': department ?? 'unknown',
        'login_method': loginMethod ?? 'unknown',
        'last_login': DateTime.now().toIso8601String(),
      };

      // إضافة معلومات الجهاز المختصرة
      userProperties.addAll(deviceSummary);

      // تسجيل خصائص المستخدم
      for (final entry in userProperties.entries) {
        if (entry.value.isNotEmpty && entry.value != 'unknown') {
          await setUserProperty(name: entry.key, value: entry.value);
        }
      }

      // تسجيل حدث تسجيل الدخول مع معلومات السياق
      await logEvent(
        name: 'user_login_with_context',
        parameters: {
          'user_role': userRole ?? 'unknown',
          'department': department ?? 'unknown',
          'login_method': loginMethod ?? 'unknown',
          'device_model': deviceSummary['device_model'] ?? 'unknown',
          'os_version': deviceSummary['os_version'] ?? 'unknown',
          'app_version': deviceSummary['app_version'] ?? 'unknown',
          'network_type': deviceSummary['network_type'] ?? 'unknown',
          'platform': deviceSummary['platform'] ?? 'unknown',
        },
      );

      if (kDebugMode) {
        debugPrint('✅ تم إعداد معلومات السياق للمستخدم: $userId');
        debugPrint(
          '📱 معلومات الجهاز: ${deviceSummary['device_model']} - ${deviceSummary['os_version']}',
        );
        debugPrint('📶 نوع الشبكة: ${deviceSummary['network_type']}');
        debugPrint('🏢 القسم: $department، الدور: $userRole');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إعداد معلومات السياق: $e');
      }
    }
  }

  /// تسجيل بداية جلسة المستخدم
  static Future<void> logSessionStart({
    required String userId,
    required String loginMethod,
    String? userRole,
    String? department,
  }) async {
    try {
      // تعيين معرف المستخدم
      await setUserId(userId);

      // تعيين خصائص المستخدم
      await setUserProperty(name: 'login_method', value: loginMethod);
      if (userRole != null) {
        await setUserProperty(name: 'user_role', value: userRole);
      }
      if (department != null) {
        await setUserProperty(name: 'department', value: department);
      }
      await setUserProperty(
        name: 'session_start_time',
        value: DateTime.now().toIso8601String(),
      );

      // تسجيل حدث بداية الجلسة
      await logEvent(
        name: 'session_start',
        parameters: {
          'login_method': loginMethod,
          'user_id': userId,
          if (userRole != null) 'user_role': userRole,
          if (department != null) 'department': department,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (kDebugMode) {
        debugPrint('🚀 تم تسجيل بداية جلسة المستخدم: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل بداية الجلسة: $e');
      }
    }
  }

  /// تسجيل انتهاء جلسة المستخدم
  static Future<void> logSessionEnd({
    required String method,
    int? sessionDurationMinutes,
    bool? dataCleared,
  }) async {
    try {
      await logEvent(
        name: 'session_end',
        parameters: {
          'logout_method': method,
          if (sessionDurationMinutes != null)
            'session_duration_minutes': sessionDurationMinutes,
          if (dataCleared != null) 'data_cleared': dataCleared,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // مسح معرف المستخدم
      await setUserId(null);

      if (kDebugMode) {
        debugPrint('🔚 تم تسجيل انتهاء جلسة المستخدم');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل انتهاء الجلسة: $e');
      }
    }
  }

  /// تسجيل انتهاء الجلسة بسبب timeout
  static Future<void> logSessionTimeout({
    int? sessionDurationMinutes,
    int? inactivityMinutes,
  }) async {
    try {
      await logEvent(
        name: 'session_timeout',
        parameters: {
          'logout_method': 'auto_timeout',
          if (sessionDurationMinutes != null)
            'session_duration_minutes': sessionDurationMinutes,
          if (inactivityMinutes != null)
            'inactivity_minutes': inactivityMinutes,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      if (kDebugMode) {
        debugPrint('⏰ تم تسجيل انتهاء الجلسة بسبب timeout');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل timeout الجلسة: $e');
      }
    }
  }

  /// تعيين خصائص مخصصة للمستخدم
  static Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      await _analytics!.setUserProperty(name: name, value: value);

      if (kDebugMode) {
        debugPrint('🏷️ تم تعيين خاصية المستخدم: $name = $value');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين خاصية المستخدم $name: $e');
      }
    }
  }

  /// إعادة تعيين بيانات Analytics (للتوحيد مع Crashlytics)
  static Future<void> resetAnalyticsData() async {
    if (_analytics == null ||
        !EnvironmentService.isAnalyticsCollectUserPropertiesEnabled()) {
      return;
    }

    try {
      // مسح معرف المستخدم
      await _analytics!.setUserId(id: null);

      // مسح خصائص المستخدم الحساسة
      await _analytics!.setUserProperty(name: 'login_method', value: null);
      await _analytics!.setUserProperty(name: 'user_role', value: null);
      await _analytics!.setUserProperty(name: 'department', value: null);
      await _analytics!.setUserProperty(
        name: 'session_start_time',
        value: null,
      );

      if (kDebugMode) {
        debugPrint('🧹 تم مسح بيانات Analytics');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في مسح بيانات Analytics: $e');
      }
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized && _analytics != null;

  /// التحقق من تفعيل Analytics
  static bool get isEnabled =>
      EnvironmentService.isFirebaseEnabled() &&
      EnvironmentService.isFirebaseAnalyticsEnabled();

  /// الحصول على معلومات حالة الخدمة
  static Map<String, dynamic> getServiceInfo() {
    return {
      'initialized': _isInitialized,
      'analytics_available': _analytics != null,
      'observer_available': _observer != null,
      'firebase_enabled': EnvironmentService.isFirebaseEnabled(),
      'analytics_enabled': EnvironmentService.isFirebaseAnalyticsEnabled(),
      'collect_events':
          EnvironmentService.isAnalyticsCollectCustomEventsEnabled(),
      'collect_user_properties':
          EnvironmentService.isAnalyticsCollectUserPropertiesEnabled(),
      'collect_screen_views':
          EnvironmentService.isAnalyticsCollectScreenViewsEnabled(),
    };
  }
}
