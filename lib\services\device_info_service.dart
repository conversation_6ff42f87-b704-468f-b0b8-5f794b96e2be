import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter/foundation.dart';

/// خدمة جمع معلومات الجهاز والنظام والشبكة
/// تجمع معلومات شاملة لإرسالها لـ Firebase Analytics و Crashlytics
class DeviceInfoService {
  static DeviceInfoService? _instance;
  static DeviceInfoService get instance {
    _instance ??= DeviceInfoService._internal();
    return _instance!;
  }

  DeviceInfoService._internal();

  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  static final NetworkInfo _networkInfo = NetworkInfo();
  static final Connectivity _connectivity = Connectivity();

  /// جمع جميع معلومات الجهاز والنظام
  static Future<Map<String, dynamic>> getAllDeviceInfo() async {
    try {
      final results = await Future.wait([
        getDeviceInfo(),
        getAppInfo(),
        getNetworkInfo(),
        getLocationInfo(),
      ]);

      return {
        'device': results[0],
        'app': results[1],
        'network': results[2],
        'location': results[3],
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في جمع معلومات الجهاز: $e');
      }
      return {};
    }
  }

  /// جمع معلومات الجهاز
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        return await _getAndroidDeviceInfo();
      } else if (Platform.isIOS) {
        return await _getIOSDeviceInfo();
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في جمع معلومات الجهاز: $e');
      }
    }
    return {};
  }

  /// معلومات الجهاز Android
  static Future<Map<String, dynamic>> _getAndroidDeviceInfo() async {
    final androidInfo = await _deviceInfo.androidInfo;

    return {
      'platform': 'Android',
      'model': androidInfo.model,
      'brand': androidInfo.brand,
      'manufacturer': androidInfo.manufacturer,
      'device': androidInfo.device,
      'product': androidInfo.product,
      'os_version': 'Android ${androidInfo.version.release}',
      'sdk_version': androidInfo.version.sdkInt.toString(),
      'security_patch': androidInfo.version.securityPatch ?? 'Unknown',
      'board': androidInfo.board,
      'bootloader': androidInfo.bootloader,
      'display': androidInfo.display,
      'fingerprint': androidInfo.fingerprint,
      'hardware': androidInfo.hardware,
      'host': androidInfo.host,
      'id': androidInfo.id,
      'tags': androidInfo.tags,
      'type': androidInfo.type,
      'is_physical_device': androidInfo.isPhysicalDevice,
      'supported_abis': androidInfo.supportedAbis,
      'supported_32bit_abis': androidInfo.supported32BitAbis,
      'supported_64bit_abis': androidInfo.supported64BitAbis,
    };
  }

  /// معلومات الجهاز iOS
  static Future<Map<String, dynamic>> _getIOSDeviceInfo() async {
    final iosInfo = await _deviceInfo.iosInfo;

    return {
      'platform': 'iOS',
      'model': iosInfo.model,
      'name': iosInfo.name,
      'system_name': iosInfo.systemName,
      'system_version': iosInfo.systemVersion,
      'localized_model': iosInfo.localizedModel,
      'identifier_for_vendor': iosInfo.identifierForVendor ?? 'Unknown',
      'is_physical_device': iosInfo.isPhysicalDevice,
      'utsname': {
        'machine': iosInfo.utsname.machine,
        'nodename': iosInfo.utsname.nodename,
        'release': iosInfo.utsname.release,
        'sysname': iosInfo.utsname.sysname,
        'version': iosInfo.utsname.version,
      },
    };
  }

  /// جمع معلومات التطبيق
  static Future<Map<String, dynamic>> getAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();

      return {
        'app_name': packageInfo.appName,
        'package_name': packageInfo.packageName,
        'version': packageInfo.version,
        'build_number': packageInfo.buildNumber,
        'build_signature': packageInfo.buildSignature,
        'installer_store': packageInfo.installerStore ?? 'Unknown',
        'build_mode': kDebugMode ? 'debug' : 'release',
        'environment': kDebugMode ? 'development' : 'production',
        'platform': Platform.operatingSystem,
        'locale': Platform.localeName,
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في جمع معلومات التطبيق: $e');
      }
      return {};
    }
  }

  /// جمع معلومات الشبكة
  static Future<Map<String, dynamic>> getNetworkInfo() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();

      final networkInfo = <String, dynamic>{
        'connectivity_type': connectivityResult.name,
        'is_connected': connectivityResult != ConnectivityResult.none,
      };

      // معلومات إضافية للـ WiFi
      if (connectivityResult == ConnectivityResult.wifi) {
        try {
          final wifiName = await _networkInfo.getWifiName();
          final wifiBSSID = await _networkInfo.getWifiBSSID();
          final wifiIP = await _networkInfo.getWifiIP();
          final wifiIPv6 = await _networkInfo.getWifiIPv6();
          final wifiSubmask = await _networkInfo.getWifiSubmask();
          final wifiGateway = await _networkInfo.getWifiGatewayIP();
          final wifiBroadcast = await _networkInfo.getWifiBroadcast();

          networkInfo.addAll({
            'wifi_name': wifiName?.replaceAll('"', '') ?? 'Unknown',
            'wifi_bssid': wifiBSSID ?? 'Unknown',
            'wifi_ip': wifiIP ?? 'Unknown',
            'wifi_ipv6': wifiIPv6 ?? 'Unknown',
            'wifi_submask': wifiSubmask ?? 'Unknown',
            'wifi_gateway': wifiGateway ?? 'Unknown',
            'wifi_broadcast': wifiBroadcast ?? 'Unknown',
          });
        } catch (e) {
          if (kDebugMode) {
            debugPrint('⚠️ تعذر الحصول على معلومات WiFi: $e');
          }
        }
      }

      return networkInfo;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في جمع معلومات الشبكة: $e');
      }
      return {'connectivity_type': 'unknown', 'is_connected': false};
    }
  }

  /// جمع معلومات الموقع (المدينة فقط)
  static Future<Map<String, dynamic>> getLocationInfo() async {
    try {
      // التحقق من الصلاحيات
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        return {
          'permission_status': 'denied',
          'city': 'Unknown',
          'country': 'Unknown',
        };
      }

      // الحصول على الموقع التقريبي
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.low, // دقة منخفضة للخصوصية
        timeLimit: const Duration(seconds: 10),
      );

      // تحويل الإحداثيات إلى اسم المدينة (يحتاج geocoding)
      // للآن سنحفظ الإحداثيات التقريبية فقط
      return {
        'permission_status': 'granted',
        'latitude_approx':
            (position.latitude * 100).round() / 100, // تقريب لحماية الخصوصية
        'longitude_approx': (position.longitude * 100).round() / 100,
        'accuracy': position.accuracy,
        'timestamp': position.timestamp.toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في جمع معلومات الموقع: $e');
      }
      return {'permission_status': 'error', 'error': e.toString()};
    }
  }

  /// تحويل المعلومات إلى تنسيق مناسب لـ Firebase
  static Map<String, String> formatForFirebase(
    Map<String, dynamic> deviceInfo,
  ) {
    final formatted = <String, String>{};

    void addToFormatted(String prefix, Map<String, dynamic> data) {
      data.forEach((key, value) {
        if (value != null) {
          final formattedKey = '${prefix}_$key';
          if (value is Map) {
            addToFormatted(formattedKey, value as Map<String, dynamic>);
          } else if (value is List) {
            formatted[formattedKey] = value.join(', ');
          } else {
            formatted[formattedKey] = value.toString();
          }
        }
      });
    }

    deviceInfo.forEach((category, data) {
      if (data is Map<String, dynamic>) {
        addToFormatted(category, data);
      }
    });

    return formatted;
  }

  /// الحصول على ملخص مختصر للجهاز
  static Future<Map<String, String>> getDeviceSummary() async {
    try {
      final deviceInfo = await getDeviceInfo();
      final appInfo = await getAppInfo();
      final networkInfo = await getNetworkInfo();

      return {
        'device_model':
            '${deviceInfo['brand'] ?? ''} ${deviceInfo['model'] ?? ''}'.trim(),
        'os_version': deviceInfo['os_version']?.toString() ?? 'Unknown',
        'app_version':
            '${appInfo['version'] ?? ''} (${appInfo['build_number'] ?? ''})',
        'platform':
            deviceInfo['platform']?.toString() ?? Platform.operatingSystem,
        'network_type':
            networkInfo['connectivity_type']?.toString() ?? 'unknown',
        'build_mode': appInfo['build_mode']?.toString() ?? 'unknown',
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إنشاء ملخص الجهاز: $e');
      }
      return {};
    }
  }
}
