import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_core/firebase_core.dart';
import 'screens/login_screen.dart';
import 'config/app_config.dart';
import 'providers/language_provider.dart';
import 'generated/l10n/app_localizations.dart';
import 'services/environment_service.dart';
import 'services/certificate_pinning_service.dart';
import 'services/auto_logout_service.dart';
import 'services/firebase_analytics_service.dart';
import 'services/firebase_crashlytics_service.dart';
import 'services/performance_monitoring_service.dart';

void main() async {
  // تأكد من تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();

  // 🔥 تهيئة Firebase
  try {
    await Firebase.initializeApp();
    if (kDebugMode) {
      debugPrint("🔥 Firebase initialized successfully!");
    }
  } catch (e) {
    if (kDebugMode) {
      debugPrint("❌ Firebase initialization failed: $e");
    }
  }

  // تهيئة الإعدادات الآمنة
  await AppConfig.initialize();

  // تهيئة Certificate Pinning
  await CertificatePinningService.initialize();

  // تهيئة Firebase Analytics ومراقبة الأداء و Crashlytics
  try {
    await FirebaseAnalyticsService.initialize();
  } catch (e) {
    if (kDebugMode) {
      debugPrint('❌ فشل في تهيئة Firebase Analytics: $e');
    }
  }

  try {
    await FirebaseCrashlyticsService.initialize();
  } catch (e) {
    if (kDebugMode) {
      debugPrint('❌ فشل في تهيئة Firebase Crashlytics: $e');
    }
  }

  try {
    await PerformanceMonitoringService.initialize();
  } catch (e) {
    if (kDebugMode) {
      debugPrint('❌ فشل في تهيئة Performance Monitoring: $e');
    }
  }

  // التحقق من الإعدادات
  EnvironmentService.getDummyValuesWarning();

  // تعيين اتجاه شريط الحالة للتصميم المصرفي
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: AppConfig.whiteColorObj,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(
    ChangeNotifierProvider(
      create: (context) => LanguageProvider(),
      child: const BankEmployeeApp(),
    ),
  );
}

/// التطبيق المصرفي الحديث لموظفي البنك
class BankEmployeeApp extends StatefulWidget {
  const BankEmployeeApp({super.key});

  /// مفتاح التنقل العام للتطبيق
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  State<BankEmployeeApp> createState() => _BankEmployeeAppState();
}

class _BankEmployeeAppState extends State<BankEmployeeApp> {
  @override
  void initState() {
    super.initState();
    // تهيئة اللغة المحفوظة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LanguageProvider>().initialize();

      // تهيئة خدمة تسجيل الخروج التلقائي
      _initializeAutoLogoutService();
    });
  }

  /// تهيئة خدمة تسجيل الخروج التلقائي
  Future<void> _initializeAutoLogoutService() async {
    try {
      await AutoLogoutService.instance.initialize(BankEmployeeApp.navigatorKey);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ خطأ في تهيئة خدمة تسجيل الخروج التلقائي: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return MaterialApp(
          title: AppConfig.appName,
          debugShowCheckedModeBanner: false,
          navigatorKey: BankEmployeeApp.navigatorKey,

          // إضافة Firebase Analytics Observer لتتبع التنقل
          navigatorObservers: FirebaseAnalyticsService.observer != null
              ? [FirebaseAnalyticsService.observer!]
              : [],

          // إعدادات الترجمة والمحلية
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar'), // العربية
            Locale('en'), // الإنجليزية
            Locale('fr'), // الفرنسية
          ],
          locale: languageProvider.locale, // اللغة المحفوظة

          theme: _buildBankTheme(),

          // الشاشة الرئيسية هي شاشة تسجيل الدخول
          home: const LoginScreen(),
        );
      },
    );
  }

  /// بناء تصميم مصرفي حديث ومتطور
  ThemeData _buildBankTheme() {
    return ThemeData(
      // تصميم التطبيق المصرفي
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConfig.primaryColorObj,
        brightness: Brightness.light,
        primary: AppConfig.primaryColorObj,
        surface: AppConfig.whiteColorObj,
        error: AppConfig.errorColorObj,
        onPrimary: AppConfig.whiteColorObj,
        onSurface: AppConfig.darkTextColorObj,
      ),
      scaffoldBackgroundColor: AppConfig.lightGrayColorObj,
      useMaterial3: true,

      // خط حديث ونظيف
      fontFamily: GoogleFonts.cairo().fontFamily,

      // تخصيص النصوص
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: AppConfig.headlineFontSize,
          fontWeight: FontWeight.bold,
          color: AppConfig.darkTextColorObj,
          letterSpacing: -0.5,
        ),
        titleLarge: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.w600,
          color: AppConfig.darkTextColorObj,
        ),
        bodyLarge: TextStyle(
          fontSize: AppConfig.bodyFontSize,
          fontWeight: FontWeight.normal,
          color: AppConfig.darkTextColorObj,
        ),
        bodyMedium: TextStyle(
          fontSize: AppConfig.captionFontSize,
          color: AppConfig.secondaryTextColorObj,
        ),
        labelSmall: TextStyle(
          fontSize: AppConfig.smallFontSize,
          color: AppConfig.secondaryTextColorObj,
        ),
      ),

      // تخصيص الأزرار المرتفعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConfig.primaryColorObj,
          foregroundColor: AppConfig.whiteColorObj,
          elevation: AppConfig.cardElevation,
          shadowColor: AppConfig.cardShadowColorObj,
          minimumSize: Size(double.infinity, AppConfig.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          textStyle: TextStyle(
            fontSize: AppConfig.bodyFontSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // تخصيص الأزرار المحددة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppConfig.primaryColorObj,
          side: BorderSide(color: AppConfig.primaryColorObj, width: 2),
          minimumSize: Size(double.infinity, AppConfig.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          textStyle: TextStyle(
            fontSize: AppConfig.bodyFontSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // تخصيص البطاقات
      cardTheme: CardThemeData(
        elevation: AppConfig.cardElevation,
        shadowColor: AppConfig.cardShadowColorObj,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        color: AppConfig.whiteColorObj,
        margin: EdgeInsets.all(AppConfig.smallSpacing),
      ),

      // تخصيص حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppConfig.whiteColorObj,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: BorderSide(color: AppConfig.dividerColorObj),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: BorderSide(color: AppConfig.dividerColorObj),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: BorderSide(color: AppConfig.primaryColorObj, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          borderSide: BorderSide(color: AppConfig.errorColorObj),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppConfig.spacing,
          vertical: AppConfig.spacing,
        ),
        hintStyle: TextStyle(
          color: AppConfig.secondaryTextColorObj,
          fontSize: AppConfig.bodyFontSize,
        ),
      ),

      // تخصيص شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: AppConfig.whiteColorObj,
        foregroundColor: AppConfig.darkTextColorObj,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: AppConfig.titleFontSize,
          fontWeight: FontWeight.bold,
          color: AppConfig.darkTextColorObj,
        ),
        iconTheme: IconThemeData(color: AppConfig.darkTextColorObj, size: 24),
      ),

      // تخصيص الفواصل
      dividerTheme: DividerThemeData(
        color: AppConfig.dividerColorObj,
        thickness: 1,
        space: 1,
      ),

      // تخصيص الأيقونات
      iconTheme: IconThemeData(color: AppConfig.primaryColorObj, size: 24),
    );
  }
}
