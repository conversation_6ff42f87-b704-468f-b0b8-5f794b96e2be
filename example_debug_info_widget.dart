import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'lib/config/app_config.dart';
import 'lib/services/environment_service.dart';

/// مثال لعرض معلومات التشخيص على الشاشة
class DebugInfoWidget extends StatelessWidget {
  const DebugInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    // عرض معلومات التشخيص فقط إذا كان مفعل في الإعدادات
    if (!AppConfig.showDebugInfo) {
      return const SizedBox.shrink(); // لا تعرض شيء
    }

    return Container(
      margin: const EdgeInsets.all(8.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // عنوان
          Row(
            children: [
              Icon(Icons.bug_report, color: Colors.red, size: 16),
              const SizedBox(width: 4),
              Text(
                'معلومات التشخيص',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // معلومات الخادم
          _buildInfoRow('خادم', EnvironmentService.getServerUrl()),
          _buildInfoRow('قاعدة البيانات', EnvironmentService.getDatabase()),
          _buildInfoRow('إصدار التطبيق', EnvironmentService.getAppVersion()),
          
          const Divider(height: 16),
          
          // حالة الإعدادات
          _buildInfoRow('وضع التشخيص', AppConfig.debugMode ? 'مفعل' : 'معطل'),
          _buildInfoRow('التسجيل المفصل', AppConfig.verboseLogging ? 'مفعل' : 'معطل'),
          _buildInfoRow('Firebase', EnvironmentService.isFirebaseEnabled() ? 'مفعل' : 'معطل'),
          _buildInfoRow('Certificate Pinning', AppConfig.enableCertificatePinning ? 'مفعل' : 'معطل'),
          
          const Divider(height: 16),
          
          // معلومات الجلسة
          _buildInfoRow('مهلة الجلسة', '${AppConfig.defaultSessionTimeoutMinutes} دقيقة'),
          _buildInfoRow('تحذير الجلسة', '${AppConfig.defaultSessionWarningMinutes} دقيقة'),
          
          // معلومات البناء
          if (kDebugMode) ...[
            const Divider(height: 16),
            _buildInfoRow('وضع البناء', 'تطوير'),
            _buildInfoRow('Platform', Theme.of(context).platform.name),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[800],
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

/// مثال لاستخدام DebugInfoWidget في شاشة
class ExampleScreenWithDebugInfo extends StatelessWidget {
  const ExampleScreenWithDebugInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال مع معلومات التشخيص'),
      ),
      body: Column(
        children: [
          // المحتوى الأساسي للشاشة
          Expanded(
            child: Center(
              child: Text('محتوى الشاشة الأساسي'),
            ),
          ),
          
          // معلومات التشخيص في الأسفل
          const DebugInfoWidget(),
        ],
      ),
    );
  }
}

/// مثال لعرض معلومات التشخيص كـ Floating Action Button
class DebugInfoFAB extends StatelessWidget {
  const DebugInfoFAB({super.key});

  @override
  Widget build(BuildContext context) {
    if (!AppConfig.showDebugInfo) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton.small(
      onPressed: () => _showDebugDialog(context),
      backgroundColor: Colors.red.withOpacity(0.8),
      child: const Icon(Icons.info_outline, color: Colors.white),
    );
  }

  void _showDebugDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.bug_report, color: Colors.red),
            const SizedBox(width: 8),
            Text('معلومات التشخيص'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('خادم: ${EnvironmentService.getServerUrl()}'),
              Text('قاعدة البيانات: ${EnvironmentService.getDatabase()}'),
              Text('إصدار: ${EnvironmentService.getAppVersion()}'),
              const SizedBox(height: 16),
              Text('وضع التشخيص: ${AppConfig.debugMode ? "مفعل" : "معطل"}'),
              Text('Firebase: ${EnvironmentService.isFirebaseEnabled() ? "مفعل" : "معطل"}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
