import 'package:flutter/material.dart';
import '../services/environment_service.dart';

/// إعدادات التطبيق المصرفي الحديث
/// يستخدم نظام متغيرات البيئة الآمن لحماية المعلومات الحساسة
class AppConfig {
  // إعدادات Odoo الآمنة - تُقرأ من متغيرات البيئة أو ملفات الإعدادات
  // لا تحتوي على معلومات حساسة مكشوفة في الكود المصدري
  static String get defaultServerUrl => EnvironmentService.getServerUrl();
  static String get defaultDatabase => EnvironmentService.getDatabase();

  // إعدادات API - تُقرأ من .env
  static int get connectionTimeout => EnvironmentService.getConnectionTimeout();
  static int get requestTimeout => EnvironmentService.getRequestTimeout();

  // إعدادات التطبيق - تُقرأ من .env
  static String get appName => EnvironmentService.getAppName();
  static String get appSubtitle => EnvironmentService.getAppSubtitle();
  static String get appVersion => EnvironmentService.getAppVersion();

  // رسائل الخطأ - تُقرأ من .env
  static String get connectionErrorMessage =>
      EnvironmentService.getConnectionErrorMessage();
  static String get authenticationErrorMessage =>
      EnvironmentService.getAuthenticationErrorMessage();
  static String get noEmployeeDataMessage =>
      EnvironmentService.getNoEmployeeDataMessage();
  static String get generalErrorMessage =>
      EnvironmentService.getGeneralErrorMessage();

  // ألوان التطبيق المصرفي الحديث - تُقرأ من .env
  static int get primaryColor => EnvironmentService.getPrimaryColor();
  static int get whiteColor => EnvironmentService.getWhiteColor();
  static int get lightGrayColor => EnvironmentService.getLightGrayColor();
  static int get darkTextColor => EnvironmentService.getDarkTextColor();
  static int get successColor => EnvironmentService.getSuccessColor();
  static int get errorColor => EnvironmentService.getErrorColor();
  static int get cardShadowColor => EnvironmentService.getCardShadowColor();
  static int get dividerColor => EnvironmentService.getDividerColor();
  static int get secondaryTextColor =>
      EnvironmentService.getSecondaryTextColor();

  // تدرجات لونية - تُقرأ من .env
  static List<int> get primaryGradient =>
      EnvironmentService.getPrimaryGradient();
  static List<int> get successGradient =>
      EnvironmentService.getSuccessGradient();
  static List<int> get cardGradient => EnvironmentService.getCardGradient();

  // أبعاد التصميم - تُقرأ من .env
  static double get borderRadius => EnvironmentService.getBorderRadius();
  static double get largeBorderRadius =>
      EnvironmentService.getLargeBorderRadius();
  static double get cardElevation => EnvironmentService.getCardElevation();
  static double get buttonHeight => EnvironmentService.getButtonHeight();
  static double get spacing => EnvironmentService.getSpacing();
  static double get largeSpacing => EnvironmentService.getLargeSpacing();
  static double get smallSpacing => EnvironmentService.getSmallSpacing();

  // أحجام الخطوط - تُقرأ من .env
  static double get headlineFontSize =>
      EnvironmentService.getHeadlineFontSize();
  static double get titleFontSize => EnvironmentService.getTitleFontSize();
  static double get bodyFontSize => EnvironmentService.getBodyFontSize();
  static double get captionFontSize => EnvironmentService.getCaptionFontSize();
  static double get smallFontSize => EnvironmentService.getSmallFontSize();

  // إعدادات Certificate Pinning - تُقرأ من .env
  static bool get enableCertificatePinning =>
      EnvironmentService.getCertificatePinningEnabled();
  static int get certificatePinningTimeout =>
      EnvironmentService.getCertificatePinningTimeout();
  static bool get allowSelfSignedCertificates =>
      EnvironmentService.getAllowSelfSignedCertificates();

  // إعدادات مهلة الجلسات - تُقرأ من .env
  static bool get enableSessionTimeout =>
      EnvironmentService.getSessionTimeoutEnabled();
  static int get defaultSessionTimeoutMinutes =>
      EnvironmentService.getSessionTimeoutMinutes();
  static int get defaultSessionWarningMinutes =>
      EnvironmentService.getSessionWarningMinutes();
  static bool get enableSessionWarnings =>
      EnvironmentService.getSessionWarningsEnabled();
  static bool get enableAutoLogout => EnvironmentService.getAutoLogoutEnabled();

  // إعدادات التطوير والتشخيص - تُقرأ من .env
  static bool get debugMode => EnvironmentService.isDebugMode();
  static bool get verboseLogging =>
      EnvironmentService.isVerboseLoggingEnabled();
  static bool get showDebugInfo => EnvironmentService.isShowDebugInfoEnabled();
  static bool get enablePerformanceOverlay =>
      EnvironmentService.isPerformanceOverlayEnabled();

  // وظائف إضافية للأمان

  /// الحصول على مفتاح API (إن وجد)
  static String get apiKey => EnvironmentService.getApiKey();

  /// التحقق من صحة الإعدادات
  static bool get isConfigurationValid =>
      EnvironmentService.validateConfiguration();

  /// تهيئة الإعدادات (يجب استدعاؤها عند بدء التطبيق)
  static Future<void> initialize() async {
    await EnvironmentService.initialize();
  }

  /// إعادة تحميل الإعدادات
  static Future<void> reload() async {
    await EnvironmentService.reload();
  }

  // ========================================
  // دوال مساعدة لإرجاع Color objects
  // ========================================

  /// الحصول على اللون الأساسي كـ Color object
  static Color get primaryColorObj => Color(primaryColor);

  /// الحصول على اللون الأبيض كـ Color object
  static Color get whiteColorObj => Color(whiteColor);

  /// الحصول على اللون الرمادي الفاتح كـ Color object
  static Color get lightGrayColorObj => Color(lightGrayColor);

  /// الحصول على لون النص الداكن كـ Color object
  static Color get darkTextColorObj => Color(darkTextColor);

  /// الحصول على لون النجاح كـ Color object
  static Color get successColorObj => Color(successColor);

  /// الحصول على لون الخطأ كـ Color object
  static Color get errorColorObj => Color(errorColor);

  /// الحصول على لون ظل البطاقة كـ Color object
  static Color get cardShadowColorObj => Color(cardShadowColor);

  /// الحصول على لون الفاصل كـ Color object
  static Color get dividerColorObj => Color(dividerColor);

  /// الحصول على لون النص الثانوي كـ Color object
  static Color get secondaryTextColorObj => Color(secondaryTextColor);

  /// الحصول على التدرج الأساسي كـ List of Color
  static List<Color> get primaryGradientColors =>
      primaryGradient.map((color) => Color(color)).toList();

  /// الحصول على تدرج النجاح كـ List of Color
  static List<Color> get successGradientColors =>
      successGradient.map((color) => Color(color)).toList();

  /// الحصول على تدرج البطاقة كـ List of Color
  static List<Color> get cardGradientColors =>
      cardGradient.map((color) => Color(color)).toList();
}
