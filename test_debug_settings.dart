import 'package:flutter/foundation.dart';
import 'lib/config/app_config.dart';
import 'lib/services/environment_service.dart';

/// ملف اختبار للتحقق من إعدادات التطوير والتشخيص
void main() async {
  // تهيئة الإعدادات
  await AppConfig.initialize();
  
  print('🔧 اختبار إعدادات التطوير والتشخيص:');
  print('=====================================');
  
  // اختبار الإعدادات من EnvironmentService
  print('📋 من EnvironmentService:');
  print('  DEBUG_MODE: ${EnvironmentService.isDebugMode()}');
  print('  VERBOSE_LOGGING: ${EnvironmentService.isVerboseLoggingEnabled()}');
  print('  SHOW_DEBUG_INFO: ${EnvironmentService.isShowDebugInfoEnabled()}');
  print('  ENABLE_PERFORMANCE_OVERLAY: ${EnvironmentService.isPerformanceOverlayEnabled()}');
  
  print('');
  
  // اختبار الإعدادات من AppConfig
  print('⚙️ من AppConfig:');
  print('  debugMode: ${AppConfig.debugMode}');
  print('  verboseLogging: ${AppConfig.verboseLogging}');
  print('  showDebugInfo: ${AppConfig.showDebugInfo}');
  print('  enablePerformanceOverlay: ${AppConfig.enablePerformanceOverlay}');
  
  print('');
  
  // اختبار إعدادات أخرى للتأكد من عمل النظام
  print('🌐 إعدادات أخرى للتحقق:');
  print('  Server URL: ${EnvironmentService.getServerUrl()}');
  print('  Database: ${EnvironmentService.getDatabase()}');
  print('  App Name: ${EnvironmentService.getAppName()}');
  print('  Firebase Enabled: ${EnvironmentService.isFirebaseEnabled()}');
  
  print('');
  print('✅ تم الانتهاء من الاختبار بنجاح!');
}
