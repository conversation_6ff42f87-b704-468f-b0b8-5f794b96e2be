# ProGuard rules for BSIC Bank Employee App
# هذا الملف يحتوي على قواعد ProGuard لحماية وتحسين التطبيق

# Flutter specific rules - شامل ومحدث
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class io.flutter.embedding.**  { *; }
-keep class io.flutter.embedding.android.** { *; }
-keep class io.flutter.embedding.engine.** { *; }

# Google Play Core rules - إصلاح المشكلة الأساسية
-keep class com.google.android.play.** { *; }
-keep class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.**
-dontwarn com.google.android.play.core.**

# إصلاح مشاكل Play Store Split
-keep class * extends com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn com.google.android.play.core.splitcompat.**
-dontwarn com.google.android.play.core.splitinstall.**
-dontwarn com.google.android.play.core.tasks.**

# Firebase rules
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }
-dontwarn com.google.firebase.**
-dontwarn com.google.android.gms.**

# Crashlytics rules
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**

# Certificate Pinning rules
-keep class io.flutter.plugins.** { *; }
-keep class com.diefferson.http_certificate_pinning.** { *; }

# Biometric authentication rules
-keep class androidx.biometric.** { *; }
-keep class androidx.fragment.app.** { *; }

# Network security rules
-keep class javax.net.ssl.** { *; }
-keep class java.security.** { *; }

# JSON serialization rules
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# General Android rules
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom attributes
-keepattributes *Annotation*,InnerClasses,Signature,Exceptions

# إضافات للأمان المصرفي
# حماية إضافية للتطبيقات المصرفية
-keep class javax.crypto.** { *; }
-keep class java.security.** { *; }
-keep class javax.net.ssl.** { *; }

# حماية الـ Reflection المستخدم في Flutter
-keepattributes RuntimeVisibleAnnotations
-keepattributes RuntimeInvisibleAnnotations
-keepattributes RuntimeVisibleParameterAnnotations
-keepattributes RuntimeInvisibleParameterAnnotations

# حماية الـ Native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# حماية الـ Enums
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# حماية الـ Parcelable
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
}

# تحسين إضافي - إزالة التحقق من الـ assertions
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
}
