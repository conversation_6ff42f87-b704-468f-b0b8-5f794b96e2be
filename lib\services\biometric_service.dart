import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'storage_service.dart';
import 'odoo_service.dart';

/// خدمة المصادقة البيومترية (البصمة، الوجه، إلخ)
class BiometricService {
  static final LocalAuthentication _localAuth = LocalAuthentication();

  /// التحقق من توفر المصادقة البيومترية على الجهاز
  static Future<bool> isDeviceSupported() async {
    try {
      return await _localAuth.isDeviceSupported();
    } catch (e) {
      return false;
    }
  }

  /// التحقق من توفر البيانات البيومترية المسجلة
  static Future<bool> canCheckBiometrics() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  /// الحصول على قائمة أنواع المصادقة البيومترية المتاحة
  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  /// التحقق من نوع المصادقة البيومترية المدعومة
  static Future<String> getSupportedBiometricType() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();

      if (availableBiometrics.contains(BiometricType.face)) {
        return 'Face ID';
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        return 'Touch ID / بصمة الإصبع';
      } else if (availableBiometrics.contains(BiometricType.iris)) {
        return 'مسح القزحية';
      } else if (availableBiometrics.isNotEmpty) {
        return 'مصادقة بيومترية';
      } else {
        return 'غير متاح';
      }
    } catch (e) {
      return 'غير معروف';
    }
  }

  /// التحقق من إمكانية استخدام المصادقة البيومترية
  static Future<BiometricStatus> checkBiometricStatus() async {
    try {
      // التحقق من دعم الجهاز
      final isSupported = await isDeviceSupported();
      if (!isSupported) {
        return BiometricStatus.notSupported;
      }

      // التحقق من إمكانية الفحص
      final canCheck = await canCheckBiometrics();
      if (!canCheck) {
        return BiometricStatus.notAvailable;
      }

      // التحقق من وجود بيانات بيومترية مسجلة
      final availableBiometrics = await getAvailableBiometrics();
      if (availableBiometrics.isEmpty) {
        return BiometricStatus.notEnrolled;
      }

      return BiometricStatus.available;
    } catch (e) {
      return BiometricStatus.unknown;
    }
  }

  /// تنفيذ المصادقة البيومترية
  static Future<BiometricAuthResult> authenticate({
    required String reason,
    bool useErrorDialogs = true,
    bool stickyAuth = true,
  }) async {
    try {
      // التحقق من الحالة أولاً
      final status = await checkBiometricStatus();
      if (status != BiometricStatus.available) {
        return BiometricAuthResult(
          success: false,
          errorMessage: _getStatusMessage(status),
          errorCode: status.toString(),
        );
      }

      // الحصول على أنواع المصادقة المتاحة
      final availableBiometrics = await getAvailableBiometrics();

      // التحقق من إمكانية استخدام المصادقة البيومترية فقط
      final canUseBiometricOnly = await BiometricService.canUseBiometricOnly();

      if (!canUseBiometricOnly) {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'لا توجد مصادقة بيومترية متاحة على هذا الجهاز',
          errorCode: 'NO_BIOMETRIC_AVAILABLE',
        );
      }

      // تحديد رسالة المصادقة حسب النوع المتاح
      String authReason = reason;
      if (availableBiometrics.contains(BiometricType.face)) {
        authReason = 'استخدم Face ID للمصادقة';
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        authReason = 'استخدم بصمة الإصبع للمصادقة';
      }

      // تنفيذ المصادقة
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: authReason,
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true, // استخدم المصادقة البيومترية فقط
        ),
      );

      return BiometricAuthResult(
        success: didAuthenticate,
        errorMessage: didAuthenticate ? null : 'فشل في التحقق من الهوية',
      );
    } on PlatformException catch (e) {
      return BiometricAuthResult(
        success: false,
        errorMessage: _getPlatformErrorMessage(e),
        errorCode: e.code,
      );
    } catch (e) {
      return BiometricAuthResult(
        success: false,
        errorMessage: 'حدث خطأ غير متوقع: ${e.toString()}',
      );
    }
  }

  /// تسجيل الدخول باستخدام البصمة
  static Future<BiometricLoginResult> loginWithBiometric() async {
    try {
      // التحقق من تفعيل البصمة
      final isEnabled = await isBiometricEnabled();
      if (!isEnabled) {
        return BiometricLoginResult(
          success: false,
          errorMessage: 'البصمة غير مفعلة',
        );
      }

      // تنفيذ المصادقة البيومترية
      final authResult = await authenticate(
        reason: 'تسجيل الدخول إلى تطبيق الموظفين',
        useErrorDialogs: true,
        stickyAuth: true,
      );

      if (!authResult.success) {
        return BiometricLoginResult(
          success: false,
          errorMessage: authResult.errorMessage,
          errorCode: authResult.errorCode,
        );
      }

      // استرجاع بيانات تسجيل الدخول المحفوظة للبصمة
      final credentials = await StorageService.getBiometricCredentials();

      // التحقق من وجود البيانات المطلوبة
      if (credentials['email'] == null || credentials['password'] == null) {
        return BiometricLoginResult(
          success: false,
          errorMessage: 'لا توجد بيانات تسجيل دخول محفوظة للبصمة',
        );
      }

      return BiometricLoginResult(
        success: true,
        email: credentials['email'],
        password: credentials['password'],
        serverUrl: credentials['serverUrl'],
        database: credentials['database'],
      );
    } catch (e) {
      return BiometricLoginResult(
        success: false,
        errorMessage: 'حدث خطأ أثناء تسجيل الدخول: ${e.toString()}',
      );
    }
  }

  /// تفعيل المصادقة البيومترية للمستخدم
  /// يتطلب بيانات تسجيل الدخول لحفظها مع البصمة
  static Future<BiometricEnableResult> enableBiometricAuth({
    String? email,
    String? password,
    String? serverUrl,
    String? database,
  }) async {
    try {
      // التحقق من الحالة
      final status = await checkBiometricStatus();
      if (status != BiometricStatus.available) {
        return BiometricEnableResult(
          success: false,
          errorMessage: _getStatusMessage(status),
          statusCode: status.toString(),
        );
      }

      // التحقق من صحة بيانات تسجيل الدخول مع خادم Odoo أولاً
      if (email != null &&
          password != null &&
          serverUrl != null &&
          database != null) {
        try {
          final odooService = OdooService(
            baseUrl: serverUrl,
            database: database,
          );
          final authResult = await odooService.authenticate(email, password);

          if (!authResult.isSuccess) {
            return BiometricEnableResult(
              success: false,
              errorMessage:
                  'بيانات تسجيل الدخول غير صحيحة. يرجى التحقق من البريد الإلكتروني وكلمة المرور.',
              statusCode: 'invalid_credentials',
            );
          }
        } catch (e) {
          return BiometricEnableResult(
            success: false,
            errorMessage:
                'فشل في التحقق من بيانات تسجيل الدخول. يرجى التأكد من الاتصال بالإنترنت.',
            statusCode: 'connection_error',
          );
        }
      }

      // اختبار المصادقة البيومترية
      final biometricAuthResult = await authenticate(
        reason: 'تفعيل تسجيل الدخول بالبصمة',
        useErrorDialogs: true,
      );

      if (biometricAuthResult.success) {
        // حفظ بيانات تسجيل الدخول للبصمة (منفصلة عن تذكر البيانات)
        if (email != null && password != null) {
          await StorageService.saveBiometricCredentials(
            email: email,
            password: password,
            serverUrl: serverUrl,
            database: database,
          );
        }

        // حفظ إعداد تفعيل البصمة
        await StorageService.setBiometricEnabled(true);
        return BiometricEnableResult(
          success: true,
          message: 'تم تفعيل البصمة بنجاح',
        );
      }

      return BiometricEnableResult(
        success: false,
        errorMessage:
            biometricAuthResult.errorMessage ?? 'فشل في المصادقة البيومترية',
        statusCode: biometricAuthResult.errorCode,
      );
    } on PlatformException catch (e) {
      // معالجة أخطاء النظام بشكل مفصل
      String errorMessage;
      String statusCode = e.code;

      switch (e.code) {
        case 'NotAvailable':
          errorMessage = 'المصادقة البيومترية غير متاحة على هذا الجهاز';
          break;
        case 'NotEnrolled':
          errorMessage = 'لا توجد بيانات بيومترية مسجلة على الجهاز';
          break;
        case 'LockedOut':
          errorMessage = 'تم قفل المصادقة البيومترية مؤقتاً';
          break;
        case 'PermanentlyLockedOut':
          errorMessage = 'تم قفل المصادقة البيومترية نهائياً';
          break;
        case 'BiometricOnlyNotSupported':
          errorMessage = 'نوع المصادقة البيومترية غير مدعوم';
          break;
        default:
          errorMessage = e.message ?? 'حدث خطأ في المصادقة البيومترية';
      }

      return BiometricEnableResult(
        success: false,
        errorMessage: errorMessage,
        statusCode: statusCode,
      );
    } catch (e) {
      String errorMessage = 'حدث خطأ أثناء تفعيل البصمة: ${e.toString()}';
      String? statusCode;

      // معالجة أخطاء محددة
      if (e.toString().contains('FragmentActivity')) {
        errorMessage =
            'خطأ تقني في التطبيق - يرجى إعادة تشغيل التطبيق وإعادة المحاولة';
        statusCode = 'FragmentActivity_Error';
      } else if (e.toString().contains('MissingPluginException')) {
        errorMessage = 'خطأ في إعداد التطبيق - يرجى إعادة تثبيت التطبيق';
        statusCode = 'Plugin_Error';
      } else if (e.toString().contains('StateError')) {
        errorMessage = 'خطأ في حالة التطبيق - يرجى إعادة تشغيل التطبيق';
        statusCode = 'State_Error';
      }

      return BiometricEnableResult(
        success: false,
        errorMessage: errorMessage,
        statusCode: statusCode,
      );
    }
  }

  /// إلغاء تفعيل المصادقة البيومترية
  static Future<void> disableBiometricAuth() async {
    await StorageService.setBiometricEnabled(false);
    // مسح بيانات البصمة المحفوظة
    await StorageService.clearBiometricCredentials();
  }

  /// التحقق من تفعيل المصادقة البيومترية
  static Future<bool> isBiometricEnabled() async {
    return await StorageService.isBiometricEnabled();
  }

  /// التحقق من إمكانية استخدام المصادقة البيومترية فقط (بدون رمز مرور)
  static Future<bool> canUseBiometricOnly() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();

      // إذا كان هناك Face ID أو Touch ID، يمكن استخدام biometricOnly
      return availableBiometrics.contains(BiometricType.face) ||
          availableBiometrics.contains(BiometricType.fingerprint) ||
          availableBiometrics.contains(BiometricType.iris);
    } catch (e) {
      return false;
    }
  }

  /// الحصول على رسالة الخطأ حسب نوع الخطأ
  static String _getPlatformErrorMessage(PlatformException e) {
    switch (e.code) {
      case auth_error.notAvailable:
        return 'المصادقة البيومترية غير متاحة على هذا الجهاز';
      case auth_error.notEnrolled:
        return 'لم يتم تسجيل أي بيانات بيومترية على الجهاز';
      case auth_error.lockedOut:
        return 'تم قفل المصادقة البيومترية مؤقتاً بسبب المحاولات الفاشلة';
      case auth_error.permanentlyLockedOut:
        return 'تم قفل المصادقة البيومترية نهائياً';
      case auth_error.biometricOnlyNotSupported:
        return 'المصادقة البيومترية فقط غير مدعومة';
      default:
        return e.message ?? 'حدث خطأ في المصادقة البيومترية';
    }
  }

  /// الحصول على رسالة الحالة
  static String _getStatusMessage(BiometricStatus status) {
    switch (status) {
      case BiometricStatus.notSupported:
        return 'الجهاز لا يدعم المصادقة البيومترية';
      case BiometricStatus.notAvailable:
        return 'المصادقة البيومترية غير متاحة حالياً';
      case BiometricStatus.notEnrolled:
        return 'لم يتم تسجيل أي بيانات بيومترية';
      case BiometricStatus.available:
        return 'المصادقة البيومترية متاحة';
      case BiometricStatus.unknown:
        return 'حالة غير معروفة';
    }
  }
}

/// حالات المصادقة البيومترية
enum BiometricStatus {
  available, // متاحة
  notSupported, // غير مدعومة
  notAvailable, // غير متاحة
  notEnrolled, // لم يتم التسجيل
  unknown, // غير معروفة
}

/// نتيجة المصادقة البيومترية
class BiometricAuthResult {
  final bool success;
  final String? errorMessage;
  final String? errorCode;

  BiometricAuthResult({
    required this.success,
    this.errorMessage,
    this.errorCode,
  });
}

/// نتيجة تسجيل الدخول بالبصمة
class BiometricLoginResult {
  final bool success;
  final String? email;
  final String? password;
  final String? serverUrl;
  final String? database;
  final String? errorMessage;
  final String? errorCode;

  BiometricLoginResult({
    required this.success,
    this.email,
    this.password,
    this.serverUrl,
    this.database,
    this.errorMessage,
    this.errorCode,
  });
}

/// نتيجة تفعيل المصادقة البيومترية
class BiometricEnableResult {
  final bool success;
  final String? message;
  final String? errorMessage;
  final String? statusCode;

  BiometricEnableResult({
    required this.success,
    this.message,
    this.errorMessage,
    this.statusCode,
  });
}
