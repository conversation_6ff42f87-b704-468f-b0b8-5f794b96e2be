import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import '../services/environment_service.dart';
import 'device_info_service.dart';

/// خدمة Firebase Crashlytics لتتبع الأخطاء والـ crashes في التطبيق
/// تتيح مراقبة استقرار التطبيق وتجميع تقارير مفصلة عن الأخطاء
class FirebaseCrashlyticsService {
  static FirebaseCrashlytics? _crashlytics;
  static bool _isInitialized = false;

  /// الحصول على مثيل Firebase Crashlytics
  static FirebaseCrashlytics? get crashlytics => _crashlytics;

  /// تهيئة Firebase Crashlytics
  static Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        debugPrint('ℹ️ Firebase Crashlytics مهيأ مسبقاً');
      }
      return;
    }

    try {
      if (kDebugMode) {
        debugPrint('🚀 بدء تهيئة Firebase Crashlytics...');
      }

      // التحقق من تفعيل Firebase Crashlytics في الإعدادات
      final isFirebaseEnabled = EnvironmentService.isFirebaseEnabled();
      final isCrashlyticsEnabled =
          EnvironmentService.isFirebaseCrashlyticsEnabled();

      if (kDebugMode) {
        debugPrint('🔧 Firebase enabled: $isFirebaseEnabled');
        debugPrint('🔧 Crashlytics enabled: $isCrashlyticsEnabled');
      }

      if (!isFirebaseEnabled || !isCrashlyticsEnabled) {
        if (kDebugMode) {
          debugPrint(
            '⏭️ تم تخطي تهيئة Crashlytics - الخدمة معطلة في الإعدادات',
          );
        }
        return;
      }

      // تهيئة Firebase Core إذا لم يكن مهيأ
      if (Firebase.apps.isEmpty) {
        if (kDebugMode) {
          debugPrint('🔥 تهيئة Firebase Core...');
        }
        await Firebase.initializeApp();
      } else {
        if (kDebugMode) {
          debugPrint(
            '✅ Firebase Core مهيأ مسبقاً (${Firebase.apps.length} apps)',
          );
        }
      }

      // تهيئة Crashlytics
      if (kDebugMode) {
        debugPrint('📊 تهيئة Crashlytics instance...');
      }
      _crashlytics = FirebaseCrashlytics.instance;

      // تعيين إعدادات جمع البيانات
      if (kDebugMode) {
        debugPrint('⚙️ تعيين إعدادات جمع البيانات...');
      }
      await _crashlytics!.setCrashlyticsCollectionEnabled(
        EnvironmentService.isFirebaseCrashlyticsEnabled(),
      );

      // تعيين معالج الأخطاء التلقائي للـ Flutter errors
      FlutterError.onError = (FlutterErrorDetails details) {
        if (kDebugMode) {
          // في وضع التطوير، اطبع الخطأ فقط
          FlutterError.presentError(details);
        } else {
          // في الإنتاج، أرسل إلى Crashlytics
          _crashlytics?.recordFlutterFatalError(details);
        }
      };

      // تعيين معالج الأخطاء للـ Dart errors
      PlatformDispatcher.instance.onError = (error, stack) {
        if (kDebugMode) {
          debugPrint('❌ خطأ Dart: $error');
          debugPrint('📍 Stack trace: $stack');
          return true;
        } else {
          _crashlytics?.recordError(error, stack, fatal: true);
          return true;
        }
      };

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('✅ تم تهيئة Firebase Crashlytics بنجاح!');
        debugPrint('🎯 الحالة: مفعل ومستعد لتسجيل الأخطاء');
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تهيئة Firebase Crashlytics: $e');
        debugPrint('📍 Stack trace: $stackTrace');
        debugPrint('🔧 معلومات التشخيص:');
        debugPrint(
          '   - Firebase enabled: ${EnvironmentService.isFirebaseEnabled()}',
        );
        debugPrint(
          '   - Crashlytics enabled: ${EnvironmentService.isFirebaseCrashlyticsEnabled()}',
        );
        debugPrint('   - Firebase apps count: ${Firebase.apps.length}');
        debugPrint('   - Is initialized: $_isInitialized');
      }

      // في حالة فشل التهيئة، تأكد من عدم ترك الحالة في وضع غير صحيح
      _crashlytics = null;
      _isInitialized = false;
    }
  }

  /// تسجيل خطأ مخصص
  static Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    bool fatal = false,
    Map<String, dynamic>? customKeys,
  }) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      // إضافة المفاتيح المخصصة
      if (customKeys != null) {
        for (final entry in customKeys.entries) {
          await _crashlytics!.setCustomKey(entry.key, entry.value);
        }
      }

      // تسجيل الخطأ
      await _crashlytics!.recordError(
        exception,
        stackTrace,
        reason: reason,
        fatal: fatal,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل الخطأ في Crashlytics: $e');
        debugPrint('📋 الخطأ الأصلي: $exception');
        debugPrint('🔍 السبب: $reason');
      }
    }
  }

  /// تسجيل رسالة مخصصة
  static Future<void> log(String message) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      await _crashlytics!.log(message);

      if (kDebugMode) {
        debugPrint('📝 تم تسجيل رسالة في Crashlytics: $message');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تسجيل الرسالة في Crashlytics: $e');
      }
    }
  }

  /// تعيين معرف المستخدم
  static Future<void> setUserId(String userId) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      await _crashlytics!.setUserIdentifier(userId);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين معرف المستخدم في Crashlytics: $e');
      }
    }
  }

  /// إعداد معلومات السياق الشاملة للمستخدم والجهاز في Crashlytics
  static Future<void> setUserContextInfo({
    required String userId,
    String? userRole,
    String? department,
    String? loginMethod,
  }) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      // تسجيل معرف المستخدم
      await setUserId(userId);

      // جمع معلومات الجهاز والنظام
      final deviceSummary = await DeviceInfoService.getDeviceSummary();
      final fullDeviceInfo = await DeviceInfoService.getAllDeviceInfo();

      // إعداد Custom Keys للمستخدم
      await setCustomKey('user_role', userRole ?? 'unknown');
      await setCustomKey('department', department ?? 'unknown');
      await setCustomKey('login_method', loginMethod ?? 'unknown');
      await setCustomKey('last_login', DateTime.now().toIso8601String());

      // إضافة معلومات الجهاز المختصرة
      for (final entry in deviceSummary.entries) {
        if (entry.value.isNotEmpty && entry.value != 'unknown') {
          await setCustomKey('device_${entry.key}', entry.value);
        }
      }

      // إضافة معلومات مفصلة للجهاز
      if (fullDeviceInfo.containsKey('device')) {
        final deviceInfo = fullDeviceInfo['device'] as Map<String, dynamic>;
        await setCustomKey(
          'device_platform',
          deviceInfo['platform'] ?? 'unknown',
        );
        await setCustomKey(
          'device_manufacturer',
          deviceInfo['manufacturer'] ?? deviceInfo['brand'] ?? 'unknown',
        );
        await setCustomKey(
          'device_is_physical',
          deviceInfo['is_physical_device']?.toString() ?? 'unknown',
        );
      }

      // إضافة معلومات الشبكة
      if (fullDeviceInfo.containsKey('network')) {
        final networkInfo = fullDeviceInfo['network'] as Map<String, dynamic>;
        await setCustomKey(
          'network_connectivity',
          networkInfo['connectivity_type'] ?? 'unknown',
        );
        await setCustomKey(
          'network_connected',
          networkInfo['is_connected']?.toString() ?? 'unknown',
        );
      }

      // إضافة معلومات التطبيق
      if (fullDeviceInfo.containsKey('app')) {
        final appInfo = fullDeviceInfo['app'] as Map<String, dynamic>;
        await setCustomKey(
          'app_version_full',
          '${appInfo['version']} (${appInfo['build_number']})',
        );
        await setCustomKey(
          'app_build_mode',
          appInfo['build_mode'] ?? 'unknown',
        );
        await setCustomKey(
          'app_environment',
          appInfo['environment'] ?? 'unknown',
        );
      }

      if (kDebugMode) {
        debugPrint(
          '✅ تم إعداد معلومات السياق في Crashlytics للمستخدم: $userId',
        );
        debugPrint(
          '📱 معلومات الجهاز: ${deviceSummary['device_model']} - ${deviceSummary['os_version']}',
        );
        debugPrint('📶 نوع الشبكة: ${deviceSummary['network_type']}');
        debugPrint('🏢 القسم: $department، الدور: $userRole');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إعداد معلومات السياق في Crashlytics: $e');
      }
    }
  }

  /// تعيين مفتاح مخصص
  static Future<void> setCustomKey(String key, dynamic value) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      await _crashlytics!.setCustomKey(key, value);

      if (kDebugMode) {
        debugPrint('🔑 تم تعيين مفتاح مخصص في Crashlytics: $key = $value');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين المفتاح المخصص في Crashlytics: $e');
      }
    }
  }

  /// تعيين عدة مفاتيح مخصصة
  static Future<void> setCustomKeys(Map<String, dynamic> keys) async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      for (final entry in keys.entries) {
        await _crashlytics!.setCustomKey(entry.key, entry.value);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في تعيين المفاتيح المخصصة في Crashlytics: $e');
      }
    }
  }

  /// إرسال crash تجريبي (للاختبار فقط)
  static Future<void> sendTestCrash() async {
    if (_crashlytics == null || !isEnabled) return;

    if (kDebugMode) {
      debugPrint('🧪 إرسال crash تجريبي...');
      throw Exception('Test crash from Firebase Crashlytics');
    }
  }

  /// تسجيل خطأ شبكة
  static Future<void> recordNetworkError({
    required String url,
    required String method,
    required int statusCode,
    required String errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'network_error',
      'url': url,
      'method': method,
      'status_code': statusCode,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('Network Error: $errorMessage'),
      StackTrace.current,
      reason: 'Network request failed: $method $url',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// تسجيل خطأ قاعدة البيانات
  static Future<void> recordDatabaseError({
    required String operation,
    required String tableName,
    required String errorMessage,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'database_error',
      'operation': operation,
      'table_name': tableName,
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('Database Error: $errorMessage'),
      StackTrace.current,
      reason: 'Database operation failed: $operation on $tableName',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// تسجيل خطأ مصادقة
  static Future<void> recordAuthError({
    required String authMethod,
    required String errorMessage,
    String? errorCode,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'auth_error',
      'auth_method': authMethod,
      'error_code': errorCode ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('Authentication Error: $errorMessage'),
      StackTrace.current,
      reason: 'Authentication failed: $authMethod',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// تسجيل خطأ واجهة المستخدم
  static Future<void> recordUIError({
    required String screenName,
    required String errorMessage,
    String? widgetName,
    Map<String, dynamic>? additionalData,
  }) async {
    final customKeys = <String, dynamic>{
      'error_type': 'ui_error',
      'screen_name': screenName,
      'widget_name': widgetName ?? 'unknown',
      'timestamp': DateTime.now().toIso8601String(),
      ...?additionalData,
    };

    await recordError(
      Exception('UI Error: $errorMessage'),
      StackTrace.current,
      reason: 'UI error in $screenName',
      fatal: false,
      customKeys: customKeys,
    );
  }

  /// إعادة تعيين بيانات Crashlytics
  static Future<void> resetCrashlyticsData() async {
    if (_crashlytics == null || !isEnabled) return;

    try {
      // مسح معرف المستخدم
      await _crashlytics!.setUserIdentifier('');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ خطأ في إعادة تعيين بيانات Crashlytics: $e');
      }
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized && _crashlytics != null;

  /// التحقق من تفعيل Crashlytics
  static bool get isEnabled =>
      EnvironmentService.isFirebaseEnabled() &&
      EnvironmentService.isFirebaseCrashlyticsEnabled();

  /// الحصول على معلومات حالة الخدمة
  static Map<String, dynamic> getServiceInfo() {
    return {
      'initialized': _isInitialized,
      'enabled': isEnabled,
      'crashlytics_available': _crashlytics != null,
      'firebase_enabled': EnvironmentService.isFirebaseEnabled(),
      'crashlytics_enabled': EnvironmentService.isFirebaseCrashlyticsEnabled(),
    };
  }
}
